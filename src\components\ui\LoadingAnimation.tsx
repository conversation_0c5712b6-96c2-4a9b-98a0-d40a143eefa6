'use client'

import React from 'react'
import { motion } from 'motion/react'

interface LoadingAnimationProps {
  type?: 'spinner' | 'dots' | 'pulse' | 'skeleton'
  size?: 'sm' | 'md' | 'lg'
  color?: string
  className?: string
}

export function LoadingAnimation({
  type = 'spinner',
  size = 'md',
  color = 'blue',
  className = ''
}: LoadingAnimationProps) {
  const getSizeClasses = () => {
    const sizeMap = {
      sm: 'w-4 h-4',
      md: 'w-8 h-8',
      lg: 'w-12 h-12'
    }
    return sizeMap[size]
  }

  const getColorClasses = () => {
    const colorMap = {
      blue: 'text-blue-600',
      white: 'text-white',
      gray: 'text-gray-600',
      green: 'text-green-600',
      red: 'text-red-600'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  if (type === 'spinner') {
    return (
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        className={`${getSizeClasses()} ${getColorClasses()} ${className}`}
      >
        <svg
          className="w-full h-full"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </motion.div>
    )
  }

  if (type === 'dots') {
    return (
      <div className={`flex space-x-1 ${className}`}>
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              delay: index * 0.2
            }}
            className={`w-2 h-2 rounded-full ${getColorClasses().replace('text-', 'bg-')}`}
          />
        ))}
      </div>
    )
  }

  if (type === 'pulse') {
    return (
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.7, 1, 0.7]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
        className={`${getSizeClasses()} rounded-full ${getColorClasses().replace('text-', 'bg-')} ${className}`}
      />
    )
  }

  if (type === 'skeleton') {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-300 rounded h-4 w-full mb-2"></div>
        <div className="bg-gray-300 rounded h-4 w-3/4 mb-2"></div>
        <div className="bg-gray-300 rounded h-4 w-1/2"></div>
      </div>
    )
  }

  return null
}
