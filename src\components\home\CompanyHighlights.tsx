"use client";

import React from "react";
import { motion } from "motion/react";
import { Shield, Award, Users, Zap, CheckCircle, Star } from "lucide-react";
import Image from "next/image";

const highlights = [
  {
    icon: Shield,
    title: "Licensed & Trusted Experts",
    description:
      "Our engineers and consultants bring deep technical knowledge and real-world design expertise to complex architectural projects.",
    features: [
      "8+ Years Industry Experience",
      // "B.E. Graduates from Top Universities",
      // "CEIG-Approved Electrical Designs",
      "Trusted by Major Commercial & Institutional Clients",
    ],
  },

  {
    icon: Users,
    title: "Integrated Multi-Disciplinary Team",
    description:
      "Our collaborative approach brings together electrical, HVAC, plumbing, fire safety, and sustainability experts under one roof.",
    features: [
      "Skilled Electrical Engineers & Draftsmen",
      "Collaborative Design Execution",
    ],
  },
  {
    icon: Zap,
    title: "Driven by Innovation & Efficiency",
    description:
      "From energy audits to smart building integration, we focus on sustainable, efficient systems that reduce operating costs and enhance building performance.",
    features: [],
  },
];

const CompanyHighlights = () => {
  return (
    <section className="py-12 md:py-16 lg:py-20 bg-gradient-to-b from-white to-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12 md:mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-3 md:px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-xs md:text-sm font-medium mb-4"
          >
            <Star className="w-3 h-3 md:w-4 md:h-4 mr-2" />
            Why Choose JS Consultants
          </motion.div>

          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 md:mb-6">
            Engineering Solutions That Shape the Future
          </h2>

          <p className="text-base md:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
            With over 8 years of experience in MEP design consultancy, we
            specialize in delivering innovative, code-compliant, and
            future-ready engineering solutions across electrical, HVAC,
            plumbing, fire safety, solar, and smart building systems.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center mb-12 md:mb-16">
          {/* Left Column - Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative order-last lg:order-first"
          >
            <div className="relative rounded-xl md:rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/images/about/office-interior.jpg"
                alt="JS Consultants Engineering Team"
                width={600}
                height={400}
                className="object-cover w-full h-auto"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/40 to-transparent"></div>
            </div>
          </motion.div>

          {/* Right Column - Highlights */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6 md:space-y-8 order-first lg:order-last"
          >
            {highlights.map((highlight, index) => (
              <motion.div
                key={highlight.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex space-x-3 md:space-x-4"
              >
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <highlight.icon className="w-5 h-5 md:w-6 md:h-6 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-2">
                    {highlight.title}
                  </h3>
                  <p className="text-sm md:text-base text-gray-600 mb-3">
                    {highlight.description}
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {highlight.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center text-xs md:text-sm text-gray-500"
                      >
                        <CheckCircle className="w-3 h-3 md:w-4 md:h-4 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CompanyHighlights;
