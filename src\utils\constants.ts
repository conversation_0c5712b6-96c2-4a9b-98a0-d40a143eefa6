// Company Information
export const COMPANY_INFO = {
  name: "JS Consultants",
  fullName: "JS Consultants MEP Engineering Services",
  tagline: "19+ Years of MEP Engineering Excellence",
  established: "2005",
  experience: "19+ years",
  description:
    "A renowned design firm that began as an Electrical Consultant in 2005 and has since evolved into a leading MEP consultancy.",
  mission:
    "JS Consultants started as a conventional MEP consulting company in India, growing with experience and providing consultant quality and commitment to projects across Southern states and nationwide.",
  coverage: "Southern states and nationwide",
};

// Hero Section Content
export const HERO_CONTENT = {
  primaryHeadline:
    "We craft energy-efficient lighting designs that elevate experiences, save costs, and delight",
  secondaryHeadline: "19+ Years of MEP Engineering Excellence",
  companyTagline:
    "From concept to completion, JS Consultants delivers innovative MEP solutions",
};



// Client Testimonials (Actual Data)
export const TESTIMONIALS = [
  {
    id: 1,
    company: "Gnanam School of Business and Hotel Gnanam",
    location: "Thanjavur",
    testimonial:
      "We, the Gnanam School of Business and Hotel Gnanam, Thanjavur have a long association with more Than 14+ years with JS Consultants. The organization stands totally committed for delivering quality output, Seamless service and value to their customers.",
    duration: "14+ years association",
    category: "Educational & Hospitality",
    logo: "/images/clients/gnanam-logo.png",
    rating: 5,
  },
  {
    id: 2,
    company: "KC Textiles",
    location: "Thiruchendur",
    testimonial:
      "During our first association, we felt the charges are expensive about. But, the electrical systems are designed very comfortably and also energy-efficiently. So, we are enjoying every moment of every day of the service that has been provided by the JS Team.",
    duration: "Long-term partnership",
    category: "Textile Industry",
    logo: "/images/clients/kc-textiles-logo.png",
    rating: 5,
  },
  {
    id: 3,
    company: "Pothys",
    location: "Multiple Locations",
    testimonial:
      "JS Consultants headed by Mr. Nagarajan have associated with Pothys for the past few years as our electrical consultants. They are energetic and enthusiastic in what they do with innovative ideas.",
    duration: "Multi-year partnership",
    category: "Retail",
    logo: "/images/clients/pothys-logo.png",
    rating: 5,
  },
  {
    id: 4,
    company: "Textile Industry Client",
    location: "Tamil Nadu",
    testimonial:
      "60 years of the textile industry, third generation... JS Sir in particular paid close attention and offered some creative suggestions, both generally and in terms of work. JS Sir has proved his service in lighting design.",
    duration: "Ongoing collaboration",
    category: "Textile Manufacturing",
    logo: "/images/clients/textile-client-logo.png",
    rating: 5,
  },
];

// Notable Projects
export const PROJECTS = [
  {
    id: 1,
    title: "Gnanam School of Business and Hotel Gnanam",
    location: "Thanjavur",
    category: "Educational & Hospitality",
    description:
      "Comprehensive MEP engineering services for educational and hospitality facilities with 14+ years of ongoing partnership.",
    services: ["Electrical", "Mechanical", "Plumbing"],
    image: "/images/projects/gnanam-project.jpg",
    year: "2010-Present",
    status: "Ongoing",
  },
  {
    id: 2,
    title: "KC Textiles",
    location: "Thiruchendur",
    category: "Industrial",
    description:
      "Energy-efficient electrical systems design for textile manufacturing facility with focus on comfort and efficiency.",
    services: ["Electrical", "Lighting Design"],
    image: "/images/projects/kc-textiles-project.jpg",
    year: "2018",
    status: "Completed",
  },
  {
    id: 3,
    title: "Pothys Retail Showrooms",
    location: "Multiple Locations",
    category: "Commercial",
    description:
      "Electrical consulting services for multiple retail showroom locations with innovative lighting solutions.",
    services: ["Electrical", "Lighting Design"],
    image: "/images/projects/pothys-project.jpg",
    year: "2019-Present",
    status: "Ongoing",
  },
  {
    id: 4,
    title: "Textile Industry Projects",
    location: "Tamil Nadu",
    category: "Industrial",
    description:
      "Various textile industry projects with specialized lighting design and electrical systems.",
    services: ["Electrical", "Lighting Design", "Power Systems"],
    image: "/images/projects/textile-industry-project.jpg",
    year: "2015-Present",
    status: "Multiple Projects",
  },
];

// Team Information
export const TEAM_MEMBERS = [
  {
    id: 1,
    name: "Mr. Nagarajan",
    position: "Founder & Principal Consultant",
    department: "Electrical Engineering",
    experience: "19+ years",
    specializations: [
      "Lighting Design",
      "Power Systems",
      "Energy Efficiency",
      "MEP Consulting",
    ],
    bio: "Founder of JS Consultants with extensive experience in electrical engineering and MEP consulting. Known for innovative lighting design solutions and energy-efficient systems.",
    image: "/images/team/nagarajan.jpg",
    achievements: [
      "Founded JS Consultants in 2005",
      "Led 100+ successful MEP projects",
      "Specialized in energy-efficient lighting design",
      "Established long-term partnerships with major clients",
    ],
  },
];

// Company Culture Values
export const CULTURE_VALUES = [
  {
    title: "Amazing Team Feel",
    description:
      "We all pull together and help each other when needed. Our collaborative environment fosters growth and innovation.",
    icon: "Users",
  },
  {
    title: "Quality Commitment",
    description:
      "Delivering quality output, seamless service and value to our customers with 19+ years of experience.",
    icon: "Award",
  },
  {
    title: "Innovation & Energy",
    description:
      "Energetic and enthusiastic approach with innovative ideas for every project challenge.",
    icon: "Lightbulb",
  },
  {
    title: "Long-term Partnerships",
    description:
      "Building lasting relationships with clients through consistent quality and reliable service delivery.",
    icon: "Handshake",
  },
];

// Contact Information
export const CONTACT_INFO = {
  address: "Chennai, Tamil Nadu, India",
  phone: "+91 XXX XXX XXXX",
  email: "<EMAIL>",
  website: "www.jsconsultants.com",
  businessHours: "Monday - Friday: 9:00 AM - 6:00 PM",
  responseTime: "We respond within 24 hours",
};

// Navigation Menu Items
export const NAVIGATION_ITEMS = [
  { name: "Home", href: "/" },
  { name: "About", href: "/about" },
  { name: "Projects", href: "/projects" },

  { name: "Contact", href: "/contact" },
];

// Call-to-Action Buttons
export const CTA_BUTTONS = [
  { text: "Request Consultation", href: "/contact", primary: true },
  { text: "View Portfolio", href: "/projects", primary: false },
  { text: "Contact Us", href: "/contact", primary: false },
];

interface Logo {
  id: string;
  name: string;
  src: string;
  alt: string;
}
export const ArchitectsLogo: Logo[] = [
  {
    id: "1",
    name: "Amar Architecture and Designs",
    src: "/images/architects/amar-architecture-and-designs.jpeg",
    alt: "amar-architecture-and-designs",
  },
  {
    id: "2",
    name: "Pencil & Monk",
    src: "/images/architects/pencil-monk.jpg",
    alt: "pencil-monk",
  },
  {
    id: "3",
    name: "DIA LIMITED",
    src: "/images/architects/dia.png",
    alt: "dia limited",
  },
  {
    id: "4",
    name: "Space Scape Architecture",
    src: "/images/architects/space-scape.jpeg",
    alt: "space-scape",
  },
  {
    id: "5",
    name: "SEED Architecture",
    src: "/images/architects/seed-architecture.jpg",
    alt: "seed-architecture",
  },
  {
    id: "6",
    name: "Shomli",
    src: "/images/architects/shomli.png",
    alt: "shomli",
  },
  {
    id: "7",
    name: "QUN",
    src: "/images/architects/qun.jpeg",
    alt: "qun",
  },
  {
    id: "8",
    name: "YRS Designs Unlimited",
    src: "/images/architects/yrs-designs.png",
    alt: "yrs-designs",
  },
  {
    id: "9",
    name: "Compose Architects",
    src: "/images/architects/compose-archi.png",
    alt: "compose-archi",
  },
  {
    id: "10",
    name: "Quadra Arch",
    src: "/images/architects/quadra-arch.png",
    alt: "quadra-arch",
  },
  {
    id: "11",
    name: "Varsha Pradeep",
    src: "/images/architects/varsha-pradeep.jpeg",
    alt: "varsha-pradeep",
  },
  {
    id: "12",
    name: "House of Lines",
    src: "/images/architects/house-of-line.png",
    alt: "house-of-line",
  },
  {
    id: "13",
    name: "KAF",
    src: "/images/architects/kaf_logo.jpg",
    alt: "KAF_LOGO",
  },
  {
    id: "14",
    name: "Design Squares",
    src: "/images/architects/design-squares.png",
    alt: "design-squares",
  },
  {
    id: "15",
    name: "Space Matrix",
    src: "/images/architects/space_matrix_logo.jpeg",
    alt: "space_matrix_logo",
  },
  {
    id: "16",
    name: "AP Architects",
    src: "/images/architects/ap-arch.jpeg",
    alt: "ap-arch",
  },
  {
    id: "17",
    name: "IDP",
    src: "/images/architects/idp.png",
    alt: "idp",
  },
  {
    id: "18",
    name: "KS Ranganath Arch",
    src: "/images/architects/ks-ranganath-arch.png",
    alt: "ks-ranganath-arch",
  },
  {
    id: "19",
    name: "Deepak Mehta Architect",
    src: "/images/architects/deepak-mehta-architect.png",
    alt: "deepak-mehta-architect",
  },
  {
    id: "20",
    name: "Chitale & Son",
    src: "/images/architects/chitale-son-bl.png",
    alt: "Chitale-Son-bl",
  },
  {
    id: "21",
    name: "PDSYNS",
    src: "/images/architects/pdsyns-logo.png",
    alt: "pdsyns-logo",
  },
  {
    id: "22",
    name: "Chettinaad Design Chennai",
    src: "/images/architects/chettinaad-design-chennai-che.png",
    alt: "chettinaad-design-chennai-che",
  },
];

interface Projects {
  slug: string;
  title: string | null;
  projecttype: string;
  status: string | null;
  location: string | null;
  client: string | null;
  Area: string | null;
  Floors: string | null;
  images: string[];
  architect?: string | null;
}

export const current_projects: Projects[] = [
  {
    slug: "anantham-silks-ramnad",
    title: "Anantham silks - Ramnad",
    projecttype: "textiles",
    status: "Completed",
    location: "Ramnad",
    client: "Anantham Silks",
    Area: "1,45,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/anantham-silks-ramnad.jpg"],
  },
  {
    slug: "jayachandran-textile-tambaram",
    title: "Jayachandran Textile Tambaram",
    projecttype: "textiles",
    status: "Completed",
    location: "Tambaram",
    client: "Jayachandran Textiles",
    Area: "72,000 Sq.ft",
    Floors: null,
    architect: "PDSyns Chennai",
    images: ["/images/projects/jayachandran-textile-tambaram.jpg"],
  },
  {
    slug: "pothys-group",
    title: "POTHYS Group",
    projecttype: "textiles",
    status: "Completed",
    location: null,
    client: "POTHYS",
    Area: "1,50,000 Sq.ft",
    Floors: null,
    architect: "Chettinaad Designs",
    images: ["/images/projects/pothys-group.png"],
  },
  {
    slug: "jayachandran-city-center",
    title: "Jayachandran City Center",
    projecttype: "textiles",
    status: "Ongoing",
    location: null,
    client: "Jayachandran Textiles",
    Area: "3,00,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/jayachandran-textile-tambaram-2.jpg"],
  },
  {
    slug: "naachiyars-silk-group",
    title: "NAACHIYARS Silk Group",
    projecttype: "textiles",
    status: "Ongoing",
    location: null,
    client: "Naachiyars",
    Area: null,
    Floors: null,
    images: ["/images/projects/naachiyars-logo.png"],
  },
  {
    slug: "ramachandran-textile-group",
    title: "Ramachandran Textile Group",
    projecttype: "textiles",
    status: "Ongoing",
    location: null,
    client: "Ramachandran",
    Area: null,
    Floors: null,
    images: ["/images/projects/ramachandran-textile-group.jpg"],
  },
  {
    slug: "mani-textile-redhills-chennai",
    title: "Mani Textile - Redhills Chennai",
    projecttype: "textiles",
    status: "Completed",
    location: "Redhills, Chennai",
    client: "Mani Textiles",
    Area: "15,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/mani-textile-redhills-chennai.png"],
  },
  {
    slug: "shoba-textile-karapakkam-omr",
    title: "Shoba Textile - Karapakkam OMR",
    projecttype: "textiles",
    status: "Completed",
    location: "Karapakkam, OMR",
    client: "Shoba Textiles",
    Area: "30,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/shoba-textile.jpg"],
  },
  {
    slug: "sri-kumaran-tex-tiruvallur",
    title: "Sri Kumaran Tex - Tiruvallur",
    projecttype: "textiles",
    status: "Completed",
    location: "Tiruvallur",
    client: "Sri Kumaran Tex",
    Area: "25,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/sri-kumaran-tex-tiruvallur.jpg"],
  },
  {
    slug: "sri-kumaran-tex-2-tiruvallur",
    title: "Sri Kumaran Tex 2 - Tiruvallur",
    projecttype: "textiles",
    status: "Completed",
    location: "Tiruvallur",
    client: "Sri Kumaran Tex",
    Area: "30,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/sri-kumaran-tex.png"],
  },
  {
    slug: "sri-kumaran-tex-mmda-chennai",
    title: "Sri Kumaran Tex - MMDA, Chennai",
    projecttype: "textiles",
    status: "Completed",
    location: "MMDA, Chennai",
    client: "Sri Kumaran Tex",
    Area: "27,500 Sq.ft",
    Floors: null,
    images: ["/images/projects/sri-kumaran-tex.png"],
  },
  {
    slug: "selvarani-textile-alangulam",
    title: "Selvarani Textile - Alangulam",
    projecttype: "textiles",
    status: "Completed",
    location: "Alangulam",
    client: "Selvarani Textiles",
    Area: "72,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/selvarani-textile-alangulam.png"],
  },
  {
    slug: "shree-niketan-v-block-anna-nagar",
    title: "SHREE NIKETAN V BLOCK - Anna Nagar",
    projecttype: "residentials",
    status: "Completed",
    location: "Anna Nagar",
    client: "NAVTOJ Builders",
    Area: null,
    Floors: null,
    images: ["/images/projects/shree-niketan-v-block.png"],
  },
  {
    slug: "nova-apartments-adyar",
    title: "NOVA Apartments - Adyar",
    projecttype: "residentials",
    status: "Completed",
    location: "Adyar",
    client: "NOVA",
    Area: null,
    Floors: null,
    images: ["/images/projects/nova-apartment.png"],
  },
  {
    slug: "lake-dugar-ambattur",
    title: "Lake Dugar (MSB - 412 Apartments)",
    projecttype: "residentials",
    status: "Completed",
    location: "Ambattur, Chennai",
    client: "Lake Dugar",
    Area: null,
    Floors: "412 Apartments",
    images: ["/images/projects/lake-dugar.png"],
  },
  {
    slug: "vgk-builders-chennai",
    title: "VGK Builders - Chennai",
    projecttype: "residentials",
    status: "Completed",
    location: "Chennai",
    client: "VGK Builders",
    Area: null,
    Floors: "100 Apartments",
    images: ["/images/projects/vgk-builders-chennai.png"],
  },
  {
    slug: "bhoomi-buildings-ecr",
    title: "BHOOMI & BUILDINGS – ECR",
    projecttype: "residentials",
    status: "Completed",
    location: "ECR",
    client: "Bhoomi & Buildings",
    Area: null,
    Floors: "5 Apartments",
    images: ["/images/projects/bhoomi-building.png"],
  },
  {
    slug: "noor-pearl-apartment-adyar",
    title: "Noor Pearl Apartment - Adyar",
    projecttype: "residentials",
    status: "Completed",
    location: "Adyar",
    client: "Arc Developers",
    Area: null,
    Floors: null,
    images: ["/images/projects/noor-pearl-apartment.png"],
  },
  {
    slug: "tvs-emerald-villa-vengaivasal",
    title: "TVS Emerald Villa at Vengaivasal",
    projecttype: "apartments",
    status: "Completed",
    location: "Vengaivasal",
    client: "TVS Emerald",
    Area: "3,18,012 Sq.ft (Total)",
    Floors: "168 Villas (G+1 Floors)",
    images: ["/images/projects/tvs-emerald-villa.png"],
  },
  {
    slug: "nova-meridian-mogappair",
    title: "NOVA Meridian - Mogappair",
    projecttype: "apartments",
    status: "Completed",
    location: "Mogappair",
    client: "NOVA",
    Area: null,
    Floors: "20 Apartments",
    images: ["/images/projects/nova-meridian-mogappair.png"],
  },
  {
    slug: "tvs-emerald-aaranya-vengaivasal",
    title: "TVS Emerald Apartment at Vengaivasal - Aaranya",
    projecttype: "apartments",
    status: "Completed",
    location: "Vengaivasal",
    client: "TVS Emerald",
    Area: "3,18,012 Sq.ft (Total)",
    Floors: "220 Flats (G+5 Floors) - 3 Blocks",
    images: ["/images/projects/tvs-emerald-apartment-at-vengaivasal.png"],
  },
  {
    slug: "tvs-emerald-udyana-medavakkam",
    title: "TVS Emerald Apartment Udyana",
    projecttype: "apartments",
    status: "Completed",
    location: "Near Medavakkam",
    client: "TVS Emerald",
    Area: null,
    Floors: "135 Flats (G+5 Floors) - 3 Blocks",
    images: ["/images/projects/tvs-emerald-apartment-udyana.png"],
  },
  {
    slug: "shreeram-viswas-navtoj",
    title: "SHREERAM VISWAS - NAVTOJ Builders",
    projecttype: "apartments",
    status: "Completed",
    location: null,
    client: "NAVTOJ Builders",
    Area: null,
    Floors: null,
    images: ["/images/projects/shreeram-viswas-navtoj.png"],
  },
  {
    slug: "vgn-costa-ecr",
    title: "VGN Costa – ECR",
    projecttype: "apartments",
    status: "Completed",
    location: "ECR",
    client: "VGN",
    Area: null,
    Floors: "96 Apartments",
    images: ["/images/projects/vgn-costa-ecr.png"],
  },
  {
    slug: "vgk-summer-garden-medavakkam",
    title: "VGK Summer Garden - Medavakkam",
    projecttype: "apartments",
    status: "Completed",
    location: "Medavakkam",
    client: "VGK Builders",
    Area: null,
    Floors: "103 Flats (G+5 Floors) - Single Block",
    images: ["/images/projects/vgr-summer-garden-medavakkam.png"],
  },
  {
    slug: "navtoj-h-block-sai-smriti",
    title: "NAVTOJ H-BLOCK at Sai Smriti",
    projecttype: "apartments",
    status: "Completed",
    location: null,
    client: "NAVTOJ Builders",
    Area: null,
    Floors: "Stilt + 5 Floors",
    images: ["/images/projects/navtoj-h-block-at-sai-smriti.png"],
  },
  {
    slug: "pristine-nest-af-block-anna-nagar",
    title: "PRISTINE NEST AF BLOCK",
    projecttype: "apartments",
    status: "Completed",
    location: "Anna Nagar",
    client: "NAVTOJ Builders",
    Area: null,
    Floors: null,
    images: ["/images/projects/pristine-nest-af-block-navtoj.png"],
  },
  {
    slug: "anand-abode-y-block-anna-nagar",
    title: "ANAND ABODE - Y BLOCK",
    projecttype: "apartments",
    status: "Completed",
    location: "Anna Nagar",
    client: "NAVTOJ Builders",
    Area: null,
    Floors: null,
    images: ["/images/projects/anand-abode-y-block-navtoj.png"],
  },
  {
    slug: "sri-chakra-y-block-anna-nagar",
    title: "SRI CHAKRA Y BLOCK",
    projecttype: "apartments",
    status: "Completed",
    location: "Anna Nagar",
    client: "NAVTOJ Builders",
    Area: null,
    Floors: null,
    images: ["/images/projects/sri-chakra-v-block-navtoj.png"],
  },
  {
    slug: "madhananthapuram-ramyam-apartments",
    title: "Madhananthapuram Ramyam Apartments",
    projecttype: "apartments",
    status: "Completed",
    location: "Madhananthapuram",
    client: null,
    Area: "Plot-a & b Sq.ft 1,10,000",
    Floors: null,
    images: ["/images/projects/madhananthapuram-ramyam-apartment.jpg"],
  },
  {
    slug: "titan-group-india",
    title: "Titan Group",
    projecttype: "commercial",
    status: "Completed",
    location: "Across India",
    client: "Titan Group",
    Area: null,
    Floors: "250+ Projects",
    images: ["/images/projects/tanishq.jpg"],
  },
  {
    slug: "titan-eye-plus-india",
    title: "Titan Eye+",
    projecttype: "commercial",
    status: "Completed",
    location: "Across India",
    client: "Titan Eye+",
    Area: null,
    Floors: null,
    images: ["/images/projects/Titan eyeplus.jpg"],
  },
  {
    slug: "taneira-showroom-india",
    title: "TANEIRA Showroom",
    projecttype: "commercial",
    status: "Completed",
    location: "Across India",
    client: "Taneira",
    Area: null,
    Floors: null,
    images: ["/images/projects/taneira-showroom.png"],
  },
  {
    slug: "poorvika-thoraipakkam",
    title: "Poorvika Appliance - Thoraipakkam",
    projecttype: "commercial",
    status: "Completed",
    location: "Thoraipakkam",
    client: "Poorvika",
    Area: "60,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/poorvika.png"],
  },
  {
    slug: "poorvika-chrompet",
    title: "Poorvika Appliance - Chrompet",
    projecttype: "commercial",
    status: "Completed",
    location: "Chrompet",
    client: "Poorvika",
    Area: "35,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/poorvika.png"],
  },
  {
    slug: "poorvika-peravallur",
    title: "Poorvika Appliance - Peravallur",
    projecttype: "commercial",
    status: "Completed",
    location: "Peravallur",
    client: "Poorvika",
    Area: "28,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/poorvika.png"],
  },
  {
    slug: "poorvika-trichy",
    title: "Poorvika Appliance - Trichy",
    projecttype: "commercial",
    status: "Completed",
    location: "Trichy",
    client: "Poorvika",
    Area: "11,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/poorvika.png"],
  },
  {
    slug: "pantaloons-kelambakkam",
    title: "Pantaloons Kelambakkam",
    projecttype: "commercial",
    status: "Completed",
    location: "Kelambakkam",
    client: "Pantaloons",
    Area: null,
    Floors: null,
    images: ["/images/projects/pantaloons.png"],
  },
  {
    slug: "revathi-furnitures-chennai",
    title: "Revathi Furnitures – Chennai",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Chennai",
    client: "Revathi Furnitures",
    Area: "12,500 Sq.ft",
    Floors: null,
    images: ["/images/projects/revathi-furnitures-chennai.png"],
  },
  {
    slug: "hyundai-car-showroom-trichy-kancheepuram",
    title: "Hyundai Car Showrooms",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Trichy & Kancheepuram",
    client: "Hyundai",
    Area: null,
    Floors: null,
    images: ["/images/projects/hundai-car-showroom.png"],
  },
  {
    slug: "sting-showrooms-india",
    title: "Sting Showrooms – Across India",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "All Over India",
    client: "Sting",
    Area: null,
    Floors: "25+ Outlets",
    images: ["/images/projects/sting-showroom.png"],
  },
  {
    slug: "fabindia-shanthi-colony-chennai",
    title: "FAB India Show Room",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Shanthi Colony, Chennai",
    client: "FAB India",
    Area: null,
    Floors: null,
    images: ["/images/projects/fab-india-showroom.png"],
  },
  {
    slug: "ford-showroom-coimbatore",
    title: "FORD Showroom – Coimbatore",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Coimbatore",
    client: "Ford",
    Area: "28,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/ford-showroom-coimbatore.png"],
  },
  {
    slug: "kanchi-kamakoti-childs-trust-hospital-nungambakkam",
    title: "Kanchi Kamakoti Childs Trust Hospital Renovation",
    projecttype: "Healthcare",
    status: "Completed",
    location: "Nungambakkam",
    client: "Kanchi Kamakoti Childs Trust",
    Area: null,
    Floors: null,
    images: ["/images/projects/kanchi-kamakoti-childs-turst-hospital.png"],
  },
  {
    slug: "chettinad-general-hospital-kelambakkam",
    title: "Chettinad General Hospital Renovation & School Development",
    projecttype: "Healthcare",
    status: "Completed",
    location: "Kelambakkam",
    client: "Chettinad Academy",
    Area: null,
    Floors: null,
    images: ["/images/projects/chettinad-general-hospital.png"],
  },
  {
    slug: "vhs-hospitals-chennai",
    title: "VHS Hospitals",
    projecttype: "Healthcare",
    status: "Completed",
    location: "Chennai",
    client: "VHS",
    Area: null,
    Floors: null,
    images: ["/images/projects/vhs-hospitals.png"],
  },
  {
    slug: "svrrgg-hospital-tirupati",
    title: "SVRRGG Hospital",
    projecttype: "Healthcare",
    status: "Completed",
    location: "Tirupati",
    client: "SVRR Government General Hospital",
    Area: null,
    Floors: null,
    images: ["/images/projects/svrrgg-hospital-tirupati.png"],
  },
  {
    slug: "aruna-cardio-care-tirunelveli",
    title: "Aruna Cardio Care",
    projecttype: "Healthcare",
    status: "Completed",
    location: "Tirunelveli",
    client: "Aruna Cardio Care",
    Area: null,
    Floors: null,
    images: ["/images/projects/aruna-cardio-care-tirunelveli.png"],
  },
  {
    slug: "seethapathy-clinic-royapetta",
    title: "SEETHAPATHY CLINIC",
    projecttype: "Healthcare",
    status: "Completed",
    location: "Royapetta",
    client: "Seethapathy Clinic",
    Area: null,
    Floors: null,
    images: ["/images/projects/seethapathy-clinic-royapetta.png"],
  },
  {
    slug: "iopex-technologies-ambattur",
    title: "IOPEX Technologies",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Ambattur",
    client: "IOPEX Technologies",
    Area: null,
    Floors: null,
    images: ["/images/projects/iopex-technologies-ambattur.png"],
  },
  {
    slug: "synergy-office-chennai",
    title: "SYNERGY Office Fitout",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Chennai",
    client: "SYNERGY",
    Area: null,
    Floors: null,
    images: ["/images/projects/synergy-office-fitout-chennai.png"],
  },
  {
    slug: "daimler-it-oragadam",
    title: "Daimler (IT)",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Oragadam",
    client: "Daimler",
    Area: null,
    Floors: null,
    images: ["/images/projects/daimler-it-oragadam.png"],
  },
  {
    slug: "koenig-it-center-chennai",
    title: "Koenig IT Center",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Chennai",
    client: "Koenig",
    Area: null,
    Floors: null,
    images: ["/images/projects/koenig-it-center.png"],
  },
  {
    slug: "scorpio-technologies-kodambakkam",
    title: "Scorpio Technologies",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Kodambakkam",
    client: "Scorpio Technologies",
    Area: null,
    Floors: null,
    images: ["/images/projects/scorpio-technologies.png"],
  },
  {
    slug: "kshema-power-chennai",
    title: "Kshema Power",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Chennai",
    client: "Kshema Power",
    Area: null,
    Floors: null,
    images: ["/images/projects/ksehma-power-chennai.png"],
  },
  {
    slug: "zebronics-head-office-chennai",
    title: "Zebronics Head Office",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Chennai",
    client: "Zebronics",
    Area: "30,700 Sq.ft",
    Floors: null,
    images: ["/images/projects/zebronics-head-office-chennai.png"],
  },
  {
    slug: "idfc-bharat-office-trichy",
    title: "IDFC Bharat Office",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Trichy",
    client: "IDFC Bharat",
    Area: "21,700 Sq.ft",
    Floors: "G+4 Floors",
    images: ["/images/projects/idfc-bharat-office-trichy.png"],
  },
  {
    slug: "bahwan-cybertek-office-omr",
    title: "Bahwan Cybertek (BCT) Office",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "OMR",
    client: "Bahwan Cybertek",
    Area: "7,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/bahwan-cybertek-office-omr.png"],
  },
  {
    slug: "synergy-office-phase-3-kochi",
    title: "Synergy Office Phase 3",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Kochi",
    client: "Synergy",
    Area: null,
    Floors: null,
    images: ["/images/projects/synergy-office-phase-3.png"],
  },
  {
    slug: "pon-pure-chemicals-office-chennai",
    title: "PON PURE Chemicals Office",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Shanthi Colony - Chennai",
    client: "PON PURE Chemicals",
    Area: null,
    Floors: null,
    images: ["/images/projects/pon-pure-chemicals-office.png"],
  },
  {
    slug: "tringapps-office-chennai",
    title: "TRINGAPPS Office",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Chennai",
    client: "TRINGAPPS",
    Area: null,
    Floors: null,
    images: ["/images/projects/tringapps-office.png"],
  },

  {
    slug: "anna-university-chennai",
    title: "Anna University",
    projecttype: "data-centers",
    status: "Completed",
    location: "Chennai",
    client: "Anna University",
    Area: null,
    Floors: null,
    images: ["/images/projects/anna-university.png"],
  },
  {
    slug: "esds-data-center",
    title: "ESDS Data Center",
    projecttype: "data-centers",
    status: "Completed",
    location: null,
    client: "ESDS",
    Area: null,
    Floors: null,
    images: ["/images/projects/esds-data-center.png"],
  },
  {
    slug: "hmcb-data-center",
    title: "HMCB Data Centre",
    projecttype: "data-centers",
    status: "Project",
    location: null,
    client: "HMCB",
    Area: null,
    Floors: null,
    images: ["/images/projects/hmcr-data-centre-project-ezhilagam.jpg"],
  },
  {
    slug: "marie",
    title: "MARIE",
    projecttype: "data-centers",
    status: "Completed",
    location: null,
    client: null,
    Area: null,
    Floors: null,
    images: ["/images/projects/hmcr-data-centre-project-ezhilagam.jpg"],
  },
  {
    slug: "ezhilagam",
    title: "EZHILAGAM",
    projecttype: "data-centers",
    status: "Completed",
    location: null,
    client: null,
    Area: null,
    Floors: null,
    images: ["/images/projects/hmcr-data-centre-project-ezhilagam.jpg"],
  },
  {
    slug: "mrf-data-center-gujarat",
    title: "MRF Data Center",
    projecttype: "data-centers",
    status: "Completed",
    location: "Gujarat",
    client: "MRF",
    Area: null,
    Floors: null,
    images: ["/images/projects/mrf-data-center-gujarat.png"],
  },
  {
    slug: "sbi-head-office",
    title: "State Bank Of India Head Office",
    projecttype: "data-centers",
    status: "Completed",
    location: null,
    client: "State Bank Of India",
    Area: null,
    Floors: null,
    images: ["/images/projects/state-bank-of-india-head-office.png"],
  },
  {
    slug: "idfc-bharat-bank-trichy",
    title: "IDFC Bharat Bank Office",
    projecttype: "data-centers",
    status: "Completed",
    location: "Trichy",
    client: "IDFC Bharat Bank",
    Area: null,
    Floors: null,
    images: ["/images/projects/idfc-bharat-office-trichy.png"],
  },
  {
    slug: "college-road-chennai",
    title: "College Road",
    projecttype: "data-centers",
    status: "Completed",
    location: "Chennai",
    client: null,
    Area: null,
    Floors: null,
    images: ["/images/projects/hmcr-data-centre-project-ezhilagam.jpg"],
  },

  {
    slug: "esab-india-ambattur",
    title: "ESAB India Pvt Ltd",
    projecttype: "industrial",
    status: "Completed",
    location: "Ambattur",
    client: "ESAB India",
    Area: null,
    Floors: null,
    images: ["/images/projects/esap-india-pvt-ltd.png"],
  },
  {
    slug: "ap-ade-park-anantapur",
    title: "AP ADE Park",
    projecttype: "industrial",
    status: "Completed",
    location: "Anantapur",
    client: null,
    Area: null,
    Floors: null,
    images: ["/images/projects/ap-ade-park-anantapur.png"],
  },
  {
    slug: "hindustan-warehouse-chennai",
    title: "Hindustan Warehouse",
    projecttype: "industrial",
    status: "Completed",
    location: "Chennai",
    client: "Hindustan",
    Area: null,
    Floors: null,
    images: ["/images/projects/hindustan-warehouse.png"],
  },
  {
    slug: "snecma-hal-bangalore",
    title: "SNECMA Hal",
    projecttype: "industrial",
    status: "Completed",
    location: "Bangalore",
    client: "SNECMA",
    Area: null,
    Floors: null,
    images: ["/images/projects/snecma-hal-bangalore.png"],
  },

  {
    slug: "houseware-industries-chennai",
    title: "Houseware Industries",
    projecttype: "industrial",
    status: "Completed",
    location: "Chennai",
    client: "Premier Group",
    Area: null,
    Floors: null,
    images: ["/images/projects/houseware-intustries.png"],
  },
  {
    slug: "horizon-packs-thindivannam",
    title: "Horizon Packs Pvt. Ltd.",
    projecttype: "industrial",
    status: "Completed",
    location: "Thindivannam",
    client: "Horizon Packs",
    Area: null,
    Floors: null,
    images: ["/images/projects/horizon-packs-pvt.png"],
  },
  {
    slug: "bright-metal-finishers-sriperumbudur",
    title: "Bright Metal Finishers",
    projecttype: "industrial",
    status: "Completed",
    location: "Sriperumbudur",
    client: "Bright Metal Finishers",
    Area: null,
    Floors: null,
    images: ["/images/projects/bright-metal-finihers.png"],
  },
  {
    slug: "seoyon-e-hwa-maraimalai-nagar",
    title: "Seoyon E-HWA",
    projecttype: "industrial",
    status: "Completed",
    location: "Maraimalai Nagar",
    client: "Seoyon E-HWA",
    Area: null,
    Floors: null,
    images: ["/images/projects/seoyon-e-hwa.jpg"],
  },
  {
    slug: "ngc-factory-sricity-tada",
    title: "NGC Factory Building",
    projecttype: "industrial",
    status: "Completed",
    location: "Sricity, Tada",
    client: "NGC",
    Area: null,
    Floors: null,
    images: ["/images/projects/ngc-factory-building.png"],
  },
  {
    slug: "mando-automotive-phase-2",
    title: "Mando Automotive Pvt Ltd",
    projecttype: "industrial",
    status: "Phase-II",
    location: "Irungattukottai",
    client: "Mando Automotive",
    Area: null,
    Floors: null,
    images: ["/images/projects/mando-automotive-pvt.png"],
  },
  {
    slug: "esr-india-industrial",
    title: "ESR India Pvt Ltd",
    projecttype: "industrial",
    status: "Completed",
    location: null,
    client: "ESR India",
    Area: null,
    Floors: null,
    images: ["/images/projects/esr-india-pvt.png"],
  },
  {
    slug: "filter-cat-products-rajpura",
    title: "Filter Cat Products",
    projecttype: "industrial",
    status: "Completed",
    location: "Rajpura",
    client: "Filter Cat Products",
    Area: null,
    Floors: null,
    images: ["/images/projects/filter-cat-products.png"],
  },
  {
    slug: "premalatha-vijayakanth-villa-chennai",
    title: "Mrs. Premalatha Vijayakanth Villa",
    projecttype: "residentials",
    status: "Completed",
    location: "Chennai",
    client: "Mrs. Premalatha Vijayakanth",
    Area: null,
    Floors: null,
    images: ["/images/projects/premaltha-vijayakanth-villa.jpg"],
  },
  {
    slug: "captain-vijayakanth-villa",
    title: "Villa of Mr. Captain Vijayakanth",
    projecttype: "residentials",
    status: "Completed",
    location: null,
    client: "Mr. Captain Vijayakanth (DMDK)",
    Area: null,
    Floors: null,
    images: ["/images/projects/premaltha-vijayakanth-villa.jpg"],
  },
  {
    slug: "anirudh-bungalow-chennai",
    title: "Music Director Mr. Anirudh Bungalow",
    projecttype: "residentials",
    status: "Completed",
    location: "Chennai",
    client: "Mr. Anirudh (Music Director)",
    Area: null,
    Floors: null,
    images: ["/images/projects/music-director-aniruth-bungalow.png"],
  },
  {
    slug: "prabhu-deva-villa-theni",
    title: "Mr. Prabhu Deva Villa",
    projecttype: "residentials",
    status: "Completed",
    location: "Theni",
    client: "Mr. Prabhu Deva (NAVAMANI Jewellers)",
    Area: null,
    Floors: null,
    images: ["/images/projects/prabhu-deva-villa.png"],
  },
  {
    slug: "maharajan-residence-chennai",
    title: "Mr. Maharajan Residence",
    projecttype: "residentials",
    status: "Completed",
    location: "Chennai",
    client: "Mr. Maharajan",
    Area: null,
    Floors: null,
    images: ["/images/projects/maharajan-residence.jpg"],
  },
  {
    slug: "proposed-murray-bungalow-chennai",
    title: "Proposed Murray Bungalow",
    projecttype: "residentials",
    status: "Completed",
    location: "Chennai",
    client: null,
    Area: null,
    Floors: null,
    images: ["/images/projects/murray-bungalow-chennai.jpg"],
  },
  {
    slug: "international-hostel-ssn-college",
    title: "International Hostel - SSN College",
    projecttype: "education",
    status: "Completed",
    location: "Chennai OMR",
    client: "SSN College",
    Area: null,
    Floors: null,
    images: ["/images/projects/ssn-college-hostel-and-canteen.png"],
  },
  {
    slug: "sishya-school-auditorium",
    title: "Sishya School Auditorium",
    projecttype: "education",
    status: "Completed",
    location: "OMR, Chennai",
    client: "Sishya School",
    Area: null,
    Floors: null,
    images: ["/images/projects/sishya-school-auditorium.png"],
  },
  {
    slug: "orchid-school-korattur",
    title: "Orchid School",
    projecttype: "education",
    status: "Completed",
    location: "Korattur, Chennai",
    client: "Orchid School",
    Area: null,
    Floors: null,
    images: ["/images/projects/orchid-school.png"],
  },
  {
    slug: "new-college-auditorium",
    title: "The New College Auditorium",
    projecttype: "education",
    status: "Revamp",
    location: null,
    client: "The New College",
    Area: null,
    Floors: null,
    images: ["/images/projects/new-college-auditorium.png"],
  },
  {
    slug: "chetlinal-sarvaloksa-school",
    title: "Chetlinal Sarvaloksa International School",
    projecttype: "education",
    status: "Completed",
    location: "OMR, Chennai",
    client: "Chetlinal Sarvaloksa",
    Area: null,
    Floors: null,
    images: ["/images/projects/chettinad-sarvalokaa-international.png"],
  },
  {
    slug: "mailam-college-library",
    title: "Mailam College (Library & Admin Block)",
    projecttype: "education",
    status: "Completed",
    location: "Tindivanam",
    client: "Mailam College",
    Area: null,
    Floors: "Administrative & Placement Floor",
    images: ["/images/projects/mailam-college.png"],
  },
  {
    slug: "st-joseph-institute-school",
    title: "St. Joseph Institute School",
    projecttype: "education",
    status: "Completed",
    location: "Semmancheri, Chennai",
    client: "St. Joseph Institute",
    Area: "1,00,000 Sq.ft",
    Floors: null,
    images: ["/images/projects/stjoseph-institute-school.png"],
  },
  {
    slug: "sacred-heart-school-moga",
    title: "Sacred Heart School",
    projecttype: "education",
    status: "Completed",
    location: "Moga, Punjab",
    client: "Sacred Heart School",
    Area: null,
    Floors: null,
    images: ["/images/projects/sacred-heart-school.png"],
  },
  {
    slug: "sri-venkateswara-university",
    title: "Sri Venkateswara University",
    projecttype: "education",
    status: "Completed",
    location: "Tirupathi",
    client: "Sri Venkateswara University",
    Area: null,
    Floors: null,
    images: ["/images/projects/sri-venkateswara-university.png"],
  },
  {
    slug: "varuna-resorts-ecr-chennai",
    title: "VARUNA Resorts",
    projecttype: "commercial",
    status: "Completed",
    location: "ECR, Chennai",
    client: "VARUNA Resorts",
    Area: null,
    Floors: null,
    images: ["/images/projects/varuna-resorts-ecr.png"],
  },
  {
    slug: "scitus-chemistry-lab-thirumazhisai",
    title: "SCITUS CHEMISTRY LAB",
    projecttype: "education",
    status: "Completed",
    location: "Thirumazhisai",
    client: "SCITUS",
    Area: null,
    Floors: null,
    images: ["/images/projects/scitus-chemistry-lab.png"],
  },
  {
    slug: "tim-inn-hotel-kancheepuram",
    title: "TIM Inn Hotel",
    projecttype: "commercial",
    status: "Completed",
    location: "Kancheepuram",
    client: "TIM Inn",
    Area: null,
    Floors: null,
    images: ["/images/projects/tm-inn-hotel.png"],
  },
  {
    slug: "i-thought-financial-consulting-office",
    title: "I thought Financial Consulting Office",
    projecttype: "it-sector-offices",
    status: "Completed",
    location: "Teynampet, Chennai",
    client: "I thought Financial Consulting",
    Area: null,
    Floors: null,
    images: ["/images/projects/i-thought-finicial-consulting-office.png"],
  },
  {
    slug: "v-five-hotel-bar-maraimalai-nagar",
    title: "V Five Hotel Bar",
    projecttype: "commercial",
    status: "Completed",
    location: "Maraimalai Nagar",
    client: "V Five",
    Area: null,
    Floors: null,
    images: ["/images/projects/v-five-hotel-bar.png"],
  },
  {
    slug: "maruthi-scan-center-velachery",
    title: "Maruthi Scan Center",
    projecttype: "healthcare",
    status: "Completed",
    location: "Velachery",
    client: "Maruthi Scan Center",
    Area: null,
    Floors: null,
    images: ["/images/projects/maruthi-scan-center.png"],
  },
  {
    slug: "fc-marina-office",
    title: "FC Marina Office",
    projecttype: "commercial",
    status: "Completed",
    location: "Avadi",
    client: "FC Marina",
    Area: null,
    Floors: null,
    images: ["/images/projects/fc-marina-office.jpg"],
  },
  {
    slug: "eagle-electronics-avadi",
    title: "Eagle Electronics",
    projecttype: "commercial",
    status: "Completed",
    location: "Avadi",
    client: "Eagle Electronics",
    Area: null,
    Floors: null,
    images: ["/images/projects/fc-marina-office.jpg"],
  },
  {
    slug: "mtc-building-office-anna-salai",
    title: "MTC Building Office",
    projecttype: "commercial",
    status: "Completed",
    location: "Anna Salai",
    client: "MTC",
    Area: null,
    Floors: null,
    images: ["/images/projects/mtc-building-office.jpg"],
  },
  {
    slug: "rg-jis-mani-mandapam-trichy",
    title: "RG Ji's Mani Mandapam",
    projecttype: "commercial",
    status: "Completed",
    location: "Trichy",
    client: "RG Ji's",
    Area: null,
    Floors: null,
    images: ["/images/projects/mani-mandapam.jpg"],
  },
  {
    slug: "tidco-office-building-chennai",
    title: "TIDCO Office Building",
    projecttype: "commercial",
    status: "Completed",
    location: "Chennai",
    client: "TIDCO",
    Area: null,
    Floors: null,
    images: ["/images/projects/tidco-office-building.png"],
  },
  {
    slug: "easun-mr-office-chennai",
    title: "EASUN MR Office",
    projecttype: "commercial",
    status: "Completed",
    location: "Chennai",
    client: "EASUN",
    Area: null,
    Floors: null,
    images: ["/images/projects/easun-mr-office.jpg"],
  },
];

export const clientLogos: Logo[] = [
  {
    id: "1",
    name: "Anantham Silks",
    src: "/images/client-logo/anantham-silks.png",
    alt: "anantham-silks",
  },
  {
    id: "2",
    name: "Anna University",
    src: "/images/client-logo/anna-university-logo.png",
    alt: "anna-university",
  },
  {
    id: "3",
    name: "AP ADE Park",
    src: "/images/client-logo/ap-ade-park.png",
    alt: "ap-ade-park",
  },
  {
    id: "65",
    name: "Apm Terminals India",
    src: "/images/client-logo/apm-terminals-india.jpg",
    alt: "apm-terminals-india",
  },
  {
    id: "4",
    name: "Arc Developers",
    src: "/images/client-logo/arc-developers.png",
    alt: "arc-developers",
  },
  {
    id: "5",
    name: "Aruna Cardiac Care",
    src: "/images/client-logo/aruna-cardiac-care.png",
    alt: "aruna-cardiac-care",
  },
  {
    id: "66",
    name: "Arun Kumaran tex",
    src: "/images/client-logo/arun-kumaran-tex.png",
    alt: "arun-kumaran-tex",
  },
  {
    id: "6",
    name: "Bhoomi Buildings",
    src: "/images/client-logo/bhoomi-buildings.png",
    alt: "bhoomi-buildings",
  },
  {
    id: "7",
    name: "Chettinad Health City",
    src: "/images/client-logo/chettinad-health-city.png",
    alt: "chettinad-health-city",
  },
  {
    id: "8",
    name: "Chettinad",
    src: "/images/client-logo/chettinad.png",
    alt: "chettinad",
  },
  {
    id: "9",
    name: "Daimler",
    src: "/images/client-logo/daimler.png",
    alt: "daimler",
  },
  {
    id: "10",
    name: "EASUN MR",
    src: "/images/client-logo/easunmr.png",
    alt: "easunmr",
  },
  {
    id: "11",
    name: "ESAB",
    src: "/images/client-logo/esab.png",
    alt: "esab",
  },
  {
    id: "12",
    name: "ESR",
    src: "/images/client-logo/esr.png",
    alt: "esr",
  },
  {
    id: "13",
    name: "Fab India",
    src: "/images/client-logo/fabindia.png",
    alt: "fabindia",
  },
  {
    id: "14",
    name: "Fastrack",
    src: "/images/client-logo/fastrack.png",
    alt: "fastrack",
  },
  {
    id: "15",
    name: "Ford",
    src: "/images/client-logo/ford.png",
    alt: "ford",
  },
  {
    id: "16",
    name: "GRT",
    src: "/images/client-logo/grt.png",
    alt: "grt",
  },
  {
    id: "17",
    name: "Horizon Packs",
    src: "/images/client-logo/horizon-packs.png",
    alt: "horizon-packs",
  },
  {
    id: "18",
    name: "Hyundai",
    src: "/images/client-logo/hyundai.png",
    alt: "hyundai",
  },
  {
    id: "19",
    name: "IDFC First Bank",
    src: "/images/client-logo/idfc-first-bank.png",
    alt: "idfc-first-bank",
  },
  {
    id: "20",
    name: "IOPEX",
    src: "/images/client-logo/iopex.png",
    alt: "iopex",
  },
  {
    id: "21",
    name: "I Thought",
    src: "/images/client-logo/ithought.png",
    alt: "ithought",
  },
  {
    id: "22",
    name: "Jayachandran",
    src: "/images/client-logo/jayachandran-logo.png",
    alt: "jayachandran-logo",
  },
  {
    id: "23",
    name: "Koenig",
    src: "/images/client-logo/koenig.png",
    alt: "koenig",
  },
  {
    id: "24",
    name: "Kshema",
    src: "/images/client-logo/kshema.png",
    alt: "kshema",
  },
  {
    id: "25",
    name: "Mando Anand",
    src: "/images/client-logo/mando-anand.png",
    alt: "mando-anand",
  },
  {
    id: "26",
    name: "MRF",
    src: "/images/client-logo/mrf.png",
    alt: "mrf",
  },
  {
    id: "27",
    name: "Naachiyars Silks",
    src: "/images/client-logo/naachiyars-silks.png",
    alt: "naachiyars-silks",
  },
  {
    id: "28",
    name: "NGC",
    src: "/images/client-logo/ngc.png",
    alt: "ngc",
  },
  {
    id: "29",
    name: "NOVA",
    src: "/images/client-logo/nova.png",
    alt: "nova",
  },
  {
    id: "30",
    name: "Orchids International School",
    src: "/images/client-logo/orchids-international-school.png",
    alt: "orchids-international-school",
  },
  {
    id: "31",
    name: "Pantaloons",
    src: "/images/client-logo/pantaloons.png",
    alt: "pantaloons",
  },
  {
    id: "32",
    name: "Pon Pure Chemicals Group",
    src: "/images/client-logo/pon-pure-chemicals-group.png",
    alt: "pon-pure-chemicals-group",
  },
  {
    id: "33",
    name: "Poorvika",
    src: "/images/client-logo/poorvika.png",
    alt: "poorvika",
  },
  {
    id: "34",
    name: "Pothys",
    src: "/images/client-logo/pothys.png",
    alt: "pothys",
  },
  {
    id: "35",
    name: "Premier Industries",
    src: "/images/client-logo/premier-industries.png",
    alt: "premier-industries",
  },
  {
    id: "36",
    name: "Ramachandran",
    src: "/images/client-logo/ramachandran-logo.png",
    alt: "ramachandran-logo",
  },
  {
    id: "37",
    name: "SBI",
    src: "/images/client-logo/sbi.png",
    alt: "sbi",
  },
  {
    id: "38",
    name: "SCITUS",
    src: "/images/client-logo/scitus.png",
    alt: "scitus",
  },
  {
    id: "39",
    name: "Scorpio Technologies",
    src: "/images/client-logo/scorpio-technologies.png",
    alt: "scorpio-technologies",
  },
  {
    id: "40",
    name: "Seethapathy Clinic",
    src: "/images/client-logo/seethapathy-clinic.png",
    alt: "seethapathy-clinic",
  },
  {
    id: "41",
    name: "Sishya OMR",
    src: "/images/client-logo/sishya-omr.png",
    alt: "sishya-omr",
  },
  {
    id: "42",
    name: "Sri Kumaran Tex",
    src: "/images/client-logo/sri-kumaran-tex.png",
    alt: "sri-kumaran-tex",
  },
  {
    id: "43",
    name: "Sri Navamani Jeweller",
    src: "/images/client-logo/sri-navamani-jeweller.png",
    alt: "sri-navamani-jeweller",
  },
  {
    id: "44",
    name: "Sri Venketesvara University",
    src: "/images/client-logo/sri-venketesvara-university.png",
    alt: "sri-venketesvara-university",
  },
  {
    id: "45",
    name: "SSN College",
    src: "/images/client-logo/ssn-college.png",
    alt: "ssn-college",
  },
  {
    id: "46",
    name: "St Joseph College",
    src: "/images/client-logo/st-joseph-college.png",
    alt: "st-joseph-college",
  },
  {
    id: "47",
    name: "Sting",
    src: "/images/client-logo/sting.png",
    alt: "sting",
  },
  {
    id: "48",
    name: "Synergy Group",
    src: "/images/client-logo/synergy-group.png",
    alt: "synergy-group",
  },
  {
    id: "49",
    name: "Tanishq",
    src: "/images/client-logo/tanishq.png",
    alt: "tanishq",
  },
  {
    id: "50",
    name: "The New College",
    src: "/images/client-logo/the-new-college.png",
    alt: "the-new-college",
  },
  {
    id: "51",
    name: "TIDCO",
    src: "/images/client-logo/tidco.png",
    alt: "tidco",
  },
  {
    id: "52",
    name: "Titan Eye Plus",
    src: "/images/client-logo/titan-eye-plus.png",
    alt: "titan-eye-plus",
  },
  {
    id: "53",
    name: "Titan",
    src: "/images/client-logo/titan.png",
    alt: "titan",
  },
  {
    id: "54",
  name: "TM Inn",
    src: "/images/client-logo/tm-inn.png",
    alt: "tm-inn",
  },
  {
    id: "55",
    name: "Tringapps",
    src: "/images/client-logo/tringapps.png",
    alt: "tringapps",
  },
  {
    id: "56",
    name: "TVS Emerald",
    src: "/images/client-logo/tvs-emerald.png",
    alt: "tvs-emerald",
  },
  {
    id: "57",
    name: "V Five Hotel",
    src: "/images/client-logo/v-five-hotel.png",
    alt: "v-five-hotel",
  },
  {
    id: "58",
    name: "Varuna Inn",
    src: "/images/client-logo/varuna-inn.png",
    alt: "varuna-inn",
  },
  {
    id: "59",
    name: "VGK Property Developers",
    src: "/images/client-logo/vgk-property-developers.png",
    alt: "vgk-property-developers",
  },
  {
    id: "60",
    name: "World of Titan",
    src: "/images/client-logo/world-of-titan.png",
    alt: "world-of-titan",
  },
  {
    id: "61",
    name: "Zebronics",
    src: "/images/client-logo/zebronics.png",
    alt: "zebronics",
  },
  {
    id: "62",
    name: "Zoho",
    src: "/images/client-logo/zoho.png",
    alt: "zoho",
  },
  {
    id: "63",
    name: "Zylog",
    src: "/images/client-logo/zylog.png",
    alt: "zylog",
  },
];

export const HeaderServices = [
  {
    id: "electrical",
    title: "Electrical",
    description:
      "Professional electrical installation, maintenance, and repair services for residential and commercial properties with cutting-edge technology solutions.",

    image:
      "/images/services/electrical.jpg",
    href: "/services/electrical",


  },
  {
    id: "hvac",
    title: "HVAC",
    description:
      "Complete heating, ventilation, and air conditioning solutions for optimal indoor climate control with energy-efficient technologies.",

    image:
      "/images/services/hvac.jpg",
    href:"/services/hvac"
  },
  {
    id: "plumbing",
    title: "Plumbing",
    description:
      "Expert plumbing solutions for all your water and drainage needs with innovative leak detection and smart water management systems.",

    image:
      "/images/services/plumbing.jpg",
    href:"/services/plumbing"
  },
  {
    id: "fire-safety",
    title: "Fire & Safety",
    description:
      "Comprehensive fire protection and safety systems using state-of-the-art technology to keep your property and people safe.",

    image:
      "/images/services/fire-safety.jpg",
      href:"/services/fire-safety"
  },
  {
    id: "energy-audit",
    title: "Energy Audit",
    description:
      "Professional energy assessments using advanced analytics to optimize efficiency and dramatically reduce operational costs.",

    image:
      "/images/services/energy-audit.jpg",
      href:"/services/energy-audit"
  },
  {
    id: "data-center",
    title: "Data Center",
    description:
      "Specialized data center infrastructure services for mission-critical operations with redundant systems and 99.9% uptime guarantee.",

    image:
      "/images/services/data-center.jpg",
      href:"/services/data-center"
  },
  {
    id: "solar",
    title: "Solar Solutions",
    description:
      "Renewable energy solutions with premium solar panel installation and smart energy management for maximum efficiency.",

    image:
      "/images/services/solar.jpg",
      href:"/services/solar"
  },
  {
    id: "ibms",
    title: "IBMS",
    description:
      "Smart building automation systems for efficient facility management with AI-powered controls and predictive maintenance.",

    image:
      "/images/services/ibms.jpg",
      href:"/services/ibms"
  },
];
