import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, Space_Mono } from "next/font/google";
import "./globals.css";
import FloatingActionButton from "@/components/ui/FloatingActionButton";
import Header from "@/components/header/Header";
import Footer from "@/components/footer/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const spaceMono = Space_Mono({
  subsets: ["latin"],
  weight: ["400", "700"], // choose weights you need
  variable: "--font-space-mono", // optional for CSS variables
  display: "swap",
});

export const metadata: Metadata = {
  title: "JS Consultants",
  description:
    "JS Consultants is a specialized electrical and MEP engineering firm committed to delivering the finest in design and execution",
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon.svg", type: "image/svg+xml" },
    ],
    apple: [{ url: "/apple-touch-icon.png" }],
  },
  manifest: "/site.webmanifest",
  themeColor: "#FFFFFF",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${spaceMono.variable} antialiased font-sans`}
      >
        <Header />
        {children}
        {/* <FloatingActionButton /> */}
        <Footer />
      </body>
    </html>
  );
}
