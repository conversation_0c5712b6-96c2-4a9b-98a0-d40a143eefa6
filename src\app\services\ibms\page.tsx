import React from "react";
import { Metadata } from "next";
import CallToActionSection from "@/components/home/<USER>";

export const metadata: Metadata = {
  title:
    "IBMS Services | Integrated Building Management Systems | Smart Building Automation | JS Consultants",
  description:
    "Professional IBMS (Integrated Building Management Systems) design services for smart building automation. Expert building automation consultants in Chennai with centralized control solutions for HVAC, lighting, fire safety, and security systems.",
  keywords:
    "IBMS, integrated building management systems, building automation, smart building controls, BMS design, HVAC automation, lighting control systems, fire safety integration, access control systems, energy management, building automation consultants Chennai",
  openGraph: {
    title: "Expert IBMS & Building Automation Services - JS Consultants",
    description:
      "Comprehensive IBMS solutions integrating HVAC, lighting, fire safety, and security systems for intelligent building management and energy efficiency.",
    type: "website",
    locale: "en_IN",
    url: "https://jsconsultants.com/services/ibms",
    siteName: "JS Consultants",
    images: [
      {
        url: "/images/services/ibms-og.jpg",
        width: 1200,
        height: 630,
        alt: "IBMS Building Automation Services by JS Consultants",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "IBMS & Building Automation Services - JS Consultants",
    description:
      "Professional IBMS solutions for smart building automation with integrated control of HVAC, lighting, fire safety, and security systems.",
    images: ["/images/services/ibms-twitter.jpg"],
  },
  alternates: {
    canonical: "https://jsconsultants.com/services/ibms",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  name: "IBMS & Building Automation Services",
  description:
    "Professional IBMS (Integrated Building Management Systems) design services for smart building automation including HVAC, lighting, fire safety, and security system integration.",
  provider: {
    "@type": "Organization",
    name: "JS Consultants",
    url: "https://jsconsultants.com",
    logo: "https://jsconsultants.com/logo.png",
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+91-98765-43210",
      contactType: "customer service",
      areaServed: "IN",
      availableLanguage: ["English", "Tamil", "Hindi"],
    },
  },
  areaServed: {
    "@type": "Country",
    name: "India",
  },
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: "IBMS & Building Automation Services",
    itemListElement: [
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Building Automation Systems",
          description:
            "Centralized control and monitoring of HVAC, lighting, fire safety, and security systems through integrated building management platforms.",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Energy Management Systems",
          description:
            "Advanced energy monitoring and optimization systems for reduced operational costs and improved building efficiency.",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Smart Building Integration",
          description:
            "Integration of IoT devices, sensors, and control systems for intelligent building operations and predictive maintenance.",
        },
      },
    ],
  },
};