import React, { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Zap,
  Wrench,
  Droplets,
  Flame,
  Shield,
  Battery,
  Sun,
  Settings,
  Database,
  Cog,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface Service {
  id: string;
  title: string;
  description: string;
  points: string[];
  cta: string;
  icon: React.ReactNode;
  image: string;
  gradient: string;
}

const ServicesOverview: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const services: Service[] = [
    {
      id: "electrical",
      title: "Electrical",
      description:
        "We provide detailed electrical design and documentation to ensure safe, efficient, and code-compliant power distribution systems for residential, commercial, and industrial projects. Our focus is on optimal performance and safety.",
      points: [
        "Electrical load calculation and power layout planning",
        "Lighting design and energy efficiency optimization",
        "LED lighting solutions & automated controls",
        "Circuit protection (MCBs, RCCBs, surge protection)",
        "Earthing and lightning protection design",
      ],
      cta: "Enquire Now",
      icon: <Zap className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1621905251918-48416bd8575a?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "hvac",
      title: "HVAC",
      description:
        "Our HVAC consultancy ensures thermal comfort and energy efficiency through intelligent heating, ventilation, and air conditioning designs tailored to your building's usage and climate requirements.",
      points: [
        "Heat load calculations and equipment sizing",
        "HVAC system layout and ducting design",
        "Energy-efficient ventilation planning",
        "Integration with Building Management Systems (BMS)",
      ],
      cta: "Enquire Now",
      icon: <Wrench className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "plumbing",
      title: "Plumbing",
      description:
        "We deliver efficient and sustainable plumbing system designs for water supply, drainage, and sanitation that comply with local and international building codes.",
      points: [
        "Water supply and distribution layout",
        "Drainage and sewage system design",
        "Rainwater harvesting and reuse systems",
        "Pipe sizing, pump selection, and system zoning",
      ],
      cta: "Enquire Now",
      icon: <Droplets className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "fire-safety",
      title: "Fire & Safety",
      description:
        "We offer complete fire safety system design, focusing on life safety, property protection, and regulatory compliance. Our designs are in line with NFPA, NBC, and other global standards.",
      points: [
        "Fire alarm and detection system planning",
        "Sprinkler and hydrant system design",
        "Egress and emergency lighting layouts",
        "Fire risk assessment and code compliance",
      ],
      cta: "Enquire Now",
      icon: <Shield className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "energy-audit",
      title: "Energy Audit",
      description:
        "We provide energy audits to evaluate building energy performance and recommend optimization strategies that reduce costs and environmental impact.",
      points: [
        "Load profiling and energy usage analysis",
        "Identification of inefficiencies and leakage points",
        "Recommendations for energy-saving upgrades",
        "Real-time energy usage monitoring systems",
        "Compliance with ECBC, BEE, and other energy codes",
      ],
      cta: "Enquire Now",
      icon: <Battery className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "data-center",
      title: "Data Center",
      description:
        "Specialized data center infrastructure services for mission-critical operations with redundant systems and 99.9% uptime guarantee.",
      points: [
        "Precision cooling systems for optimal server performance",
        "Redundant power distribution & UPS systems",
        "Structured cabling & high-speed networking solutions",
        "Advanced backup power systems & generators",
      ],
      cta: "Enquire Now",
      icon: <Database className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "solar",
      title: "Solar Solutions",
      description:
        "Our solar energy consultancy covers design and feasibility assessment for rooftop and utility-scale PV systems, helping clients transition to sustainable energy.",
      points: [
        "Solar potential analysis and system sizing",
        "Grid-tied and off-grid PV system layout",
        "Electrical integration with existing systems",
        "ROI estimation and regulatory guidance",
      ],
      cta: "Enquire Now",
      icon: <Sun className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "ibms",
      title: "IBMS (Intelligent Building Management)",
      description:
        "We design centralized systems that monitor and manage all building services for smarter, more efficient operations and seamless user experience",
      points: [
        "Integration of HVAC, lighting, fire safety, and access control",
        "Control logic and interface design for BMS platforms",
        "System architecture planning and documentation",
        "Coordination with automation and IT systems",
      ],
      cta: "Enquire Now",
      icon: <Settings className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
  ];

  const nextSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev + 1) % services.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const prevSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev - 1 + services.length) % services.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const goToSlide = (index: number) => {
    if (isAnimating || index === currentIndex) return;
    setIsAnimating(true);
    setCurrentIndex(index);
    setTimeout(() => setIsAnimating(false), 500);
  };

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 8000);
    return () => clearInterval(interval);
  }, [isAnimating]);

  const currentService = services[currentIndex];

  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 bg-white">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-8 md:mb-12 lg:mb-16"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="inline-flex items-center px-3 md:px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-xs md:text-sm font-medium mb-4"
        >
          <Cog className="w-3 h-3 md:w-4 md:h-4 mr-2" />
          Our Core Services
        </motion.div>

        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 md:mb-6">
          Comprehensive MEP Engineering Solutions
        </h2>

        <p className="text-base md:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
          From initial concept to final commissioning, we deliver integrated
          mechanical, electrical, and plumbing engineering services that exceed
          industry standards.
        </p>
      </motion.div>

      {/* Services Navigation Bar */}
      <div className="mb-8 md:mb-12">
        <div className="flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8">
          {services.map((service, index) => (
            <button
              key={service.id}
              onClick={() => goToSlide(index)}
              className={`group relative flex items-center space-x-2 md:space-x-3 px-3 md:px-6 py-3 md:py-4 rounded-xl md:rounded-2xl transition-all duration-300 transform hover:scale-105 text-sm md:text-base ${
                index === currentIndex
                  ? `bg-gradient-to-r ${service.gradient} text-white shadow-2xl`
                  : "bg-white hover:bg-gray-50 text-gray-700 shadow-md hover:shadow-lg border border-gray-200"
              }`}
            >
              <div
                className={`transition-all duration-300 ${
                  index === currentIndex
                    ? "text-white"
                    : "text-gray-500 group-hover:text-gray-700"
                }`}
              >
                {React.cloneElement(service.icon as React.ReactElement, {
                  className:
                    index === currentIndex
                      ? "fill-white stroke-gray-700"
                      : "text-gray-500",
                })}
              </div>
              <span className="font-semibold text-xs md:text-sm whitespace-nowrap">
                {service.title}
              </span>
              {/* {index === currentIndex && (
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full shadow-lg"></div>
              )} */}
            </button>
          ))}
        </div>
      </div>
      {/* <div>
           <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={prevSlide}
          disabled={isAnimating}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 p-4 rounded-full bg-white shadow-xl hover:shadow-2xl text-gray-700 hover:text-gray-900 transition-all duration-300 disabled:opacity-50 z-30 "
        >
          <ChevronLeft className="w-6 h-6" />
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={nextSlide}
          disabled={isAnimating}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 p-4 rounded-full bg-white shadow-xl hover:shadow-2xl text-gray-700 hover:text-gray-900 transition-all duration-300 disabled:opacity-50 z-30"
        >
          <ChevronRight className="w-6 h-6" />
        </motion.button>
      </div> */}

      {/* Main Service Card */}
      <div className="relative overflow-hidden bg-white">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
              duration: 0.5,
            }}
            className="bg-white rounded-3xl shadow-md overflow-hidden"
          >
            <div className="relative">
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
              <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-50 to-transparent rounded-full opacity-50"></div>

              <div className="relative flex flex-col lg:flex-row min-h-[400px] md:min-h-[500px] lg:min-h-[600px]">
                {/* Left Side - Service Details */}
                <motion.div
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="flex-1 p-6 md:p-8 lg:p-12 xl:p-16 flex flex-col justify-center"
                >
                  <div className="mb-6 md:mb-8">
                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                      className={`inline-flex items-center space-x-3 md:space-x-4 px-4 md:px-6 py-2 md:py-3 rounded-xl md:rounded-2xl bg-gradient-to-r ${currentService.gradient} text-white mb-4 md:mb-6 shadow-lg`}
                    >
                      {currentService.icon}
                      <span className="font-bold text-base md:text-lg">
                        {currentService.title}
                      </span>
                    </motion.div>

                    <motion.p
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.6 }}
                      className="text-gray-700 text-base md:text-lg lg:text-xl leading-relaxed mb-6 md:mb-8"
                    >
                      {currentService.description}
                    </motion.p>
                  </div>

                  <div className="mb-8 md:mb-10">
                    {/* <motion.h4
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.6 }}
                      className="text-2xl font-bold text-gray-800 mb-6"
                    >
                      Premium Features:
                    </motion.h4> */}
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.6, duration: 0.6 }}
                      className="space-y-3 md:space-y-4"
                    >
                      {currentService.points.map((point, index) => (
                        <motion.div
                          key={index}
                          initial={{ x: -20, opacity: 0 }}
                          animate={{ x: 0, opacity: 1 }}
                          transition={{
                            delay: 0.7 + index * 0.1,
                            duration: 0.5,
                          }}
                          className="flex items-start group"
                        >
                          <div
                            className={`w-3 h-3 rounded-full bg-gradient-to-r ${currentService.gradient} mt-2 mr-4 flex-shrink-0 shadow-md group-hover:scale-110 transition-transform duration-200`}
                          ></div>
                          <span className="text-gray-700 text-lg leading-relaxed group-hover:text-gray-900 transition-colors duration-200">
                            {point}
                          </span>
                        </motion.div>
                      ))}
                    </motion.div>
                  </div>

                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.6 }}
                    className="flex flex-col sm:flex-row items-center gap-3 md:gap-4"
                  >
                    <button
                      className={`bg-gradient-to-r ${currentService.gradient} hover:shadow-xl text-white px-6 md:px-8 lg:px-10 py-3 md:py-4 rounded-xl md:rounded-2xl font-bold text-sm md:text-base lg:text-lg transition-all duration-300 transform hover:scale-105 w-full sm:w-auto`}
                    >
                      {currentService.cta}
                    </button>
                    <button className="border-2 border-gray-300 hover:border-gray-400 text-gray-700 px-6 md:px-8 py-3 md:py-4 rounded-xl md:rounded-2xl font-semibold transition-all duration-300 hover:bg-gray-50 text-sm md:text-base w-full sm:w-auto">
                      Learn More
                    </button>
                  </motion.div>
                </motion.div>

                {/* Right Side - Service Image */}
                <motion.div
                  initial={{ x: 50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="flex-1 relative overflow-hidden order-first lg:order-last"
                >
                  <motion.img
                    initial={{ scale: 1.1 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, duration: 0.8 }}
                    src={currentService.image}
                    alt={currentService.title}
                    className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                  />
                  {/* Decorative Elements */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent z-10 pointer-events-none rounded-3xl"></div>
                  {/* <div
                    className={`absolute top-4 right-4 md:top-8 md:right-8 w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br ${currentService.gradient} rounded-xl md:rounded-2xl flex items-center justify-center text-white shadow-lg z-20`}
                  >
                    {currentService.icon}
                  </div> */}
                </motion.div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
      </div>

      {/* Progress Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.6 }}
        className="flex justify-center mt-8 md:mt-12"
      >
        <div className="flex space-x-1 md:space-x-2">
          {services.map((_, index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1.1 + index * 0.1, duration: 0.3 }}
              className={`h-1.5 md:h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? `w-6 md:w-8 bg-gradient-to-r ${currentService.gradient}`
                  : "w-1.5 md:w-2 bg-gray-300"
              }`}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default ServicesOverview;
