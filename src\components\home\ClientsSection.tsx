"use client";

import { motion } from "framer-motion";
import { useRef } from "react";
import Image from "next/image";

interface ClientLogo {
  id: string;
  name: string;
  src: string;
  alt: string;
}

interface ClientCarouselProps {
  clients?: ClientLogo[];
  className?: string;
}

const ClientsSection: React.FC<ClientCarouselProps> = ({
  clients = sampleClients,
  className = "",
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Create different sequences for each row
  const firstRowClients = [...clients, ...clients, ...clients, ...clients];
  
  // Create a different sequence for the second row by starting from middle
  const midpoint = Math.floor(clients.length / 2);
  const reorderedClients = [...clients.slice(midpoint), ...clients.slice(0, midpoint)];
  const secondRowClients = [...reorderedClients, ...reorderedClients, ...reorderedClients, ...reorderedClients];

  return (
    <div
      ref={containerRef}
      className={`w-full bg-white overflow-hidden ${className}`}
    >
      <div className="space-y-8">
        {/* First Row - Left to Right */}
        <div className="relative">
          <motion.div
            className="flex gap-8 w-max"
            animate={{
              x: [0, -10 + "%"],
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 30,
                ease: "linear",
              },
            }}
          >
            {firstRowClients.map((client, index) => (
              <div
                key={`${client.id}-${index}`}
                className="flex-shrink-0 w-32 h-16 relative  transition-all duration-300"
              >
                <Image
                  src={client.src}
                  alt={client.alt}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 128px, 128px"
                  loading="lazy"
                />
              </div>
            ))}
          </motion.div>
        </div>

        {/* Second Row - Right to Left with different sequence */}
        <div className="relative">
          <motion.div
            className="flex gap-8 w-max"
            animate={{
              x: [-10 + "%", 0],
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 30,
                ease: "linear",
              },
            }}
          >
            {secondRowClients.map((client, index) => (
              <div
                key={`${client.id}-reverse-${index}`}
                className="flex-shrink-0 w-32 h-16 relative  transition-all duration-300"
              >
                <Image
                  src={client.src}
                  alt={client.alt}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 128px, 128px"
                />
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ClientsSection;

export const sampleClients: ClientLogo[] = [
  {
    id: "1",
    name: "Company A",
    src: "/images/placeholder.jpg",
    alt: "Company A Logo",
  },
  {
    id: "2",
    name: "Company B",
    src: "/images/placeholder.jpg",
    alt: "Company B Logo",
  },
  {
    id: "3",
    name: "Company C",
    src: "/images/placeholder.jpg",
    alt: "Company C Logo",
  },
  {
    id: "4",
    name: "Company D",
    src: "/images/placeholder.jpg",
    alt: "Company D Logo",
  },
  {
    id: "5",
    name: "Company E",
    src: "/images/placeholder.jpg",
    alt: "Company E Logo",
  },
  {
    id: "6",
    name: "Company F",
    src: "/images/placeholder.jpg",
    alt: "Company F Logo",
  },
];