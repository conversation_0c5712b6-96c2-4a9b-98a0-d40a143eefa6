'use client'

import React from 'react'
import { motion, MotionProps } from 'motion/react'

interface HoverAnimationProps extends MotionProps {
  children: React.ReactNode
  scale?: number
  y?: number
  rotate?: number
  duration?: number
  className?: string
  type?: 'scale' | 'lift' | 'rotate' | 'glow' | 'custom'
}

export function HoverAnimation({
  children,
  scale = 1.05,
  y = -5,
  rotate = 0,
  duration = 0.3,
  className = '',
  type = 'scale',
  ...motionProps
}: HoverAnimationProps) {
  const getHoverAnimation = () => {
    switch (type) {
      case 'scale':
        return { scale }
      case 'lift':
        return { y, scale: 1.02 }
      case 'rotate':
        return { rotate, scale: 1.02 }
      case 'glow':
        return { 
          scale: 1.02,
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
        }
      case 'custom':
        return motionProps.whileHover || {}
      default:
        return { scale }
    }
  }

  return (
    <motion.div
      whileHover={getHoverAnimation()}
      whileTap={{ scale: 0.98 }}
      transition={{ duration, ease: 'easeInOut' }}
      className={className}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}
