"use client";

import React, { useEffect, useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import { useInView } from "react-intersection-observer";
import Link from "next/link";
import Header from "@/components/header/Header";
import Footer from "@/components/footer/Footer";
import ProjectCarousel from "@/components/projects/ProjectCarousel";
import { current_projects } from "@/utils/constants";
import {
  Filter,
  ExternalLink,
  Calendar,
  MapPin,
  Building,
  Users,
  Award,
  DollarSign,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  X,
} from "lucide-react";
import Image from "next/image";

const projectFilter = [
  { label: "All", filter: "all" },
  // { label: "Worship", filter: "worship" },
  { label: "Education", filter: "education" },
  { label: "Industrial", filter: "industrial" },
  { label: "Commercial", filter: "commercial" },
  { label: "Textiles", filter: "textiles" },
  { label: "Apartments", filter: "apartments" },
  { label: "Residentials", filter: "residentials" },
  { label: "Commercials", filter: "commercial" },
  { label: "IT Sector & Offices", filter: "it-sector-offices" },
  { label: "Healthcare", filter: "healthcare" },
  { label: "Data Centers", filter: "data-centers" },
];

const PROJECTS_PER_PAGE = 9;

const ProjectsPage = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  const [projectsRef, projectsInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  
  // State management
  const [filteredProjects, setFilteredProjects] = useState(current_projects);
  const [selectedProjectFilter, setSelectedProjectFilter] = useState("all");
  const [selectedProject, setSelectedProject] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate pagination
  const totalPages = Math.ceil(filteredProjects.length / PROJECTS_PER_PAGE);
  const startIndex = (currentPage - 1) * PROJECTS_PER_PAGE;
  const endIndex = startIndex + PROJECTS_PER_PAGE;
  const currentProjects = filteredProjects.slice(startIndex, endIndex);

  // Filter projects based on selected filter
  useEffect(() => {
    if (selectedProjectFilter === "all") {
      setFilteredProjects(current_projects);
    } else {
      const filtered = current_projects.filter(
        (project) => project.projecttype.toLowerCase() === selectedProjectFilter.toLowerCase()
      );
      setFilteredProjects(filtered);
    }
    // Reset to first page when filter changes
    setCurrentPage(1);
  }, [selectedProjectFilter]);

  const handleProjectClick = (project) => {
    setSelectedProject(project);
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setSelectedProject(null);
  };

  const handleFilterChange = (filter) => {
    setSelectedProjectFilter(filter);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Scroll to projects section when page changes
    projectsRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getPaginationButtons = () => {
    const buttons = [];
    const maxVisibleButtons = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisibleButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxVisibleButtons - 1);
    
    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxVisibleButtons) {
      startPage = Math.max(1, endPage - maxVisibleButtons + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(i);
    }
    
    return buttons;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section ref={heroRef} className="relative py-12 md:py-16 lg:py-20 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/projects/project-1.jpg"
            alt="Professional MEP Engineering Projects Portfolio"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-4 md:space-y-6"
          >
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white">
              Our <span className="text-blue-400">Engineering Projects</span>
            </h1>
            <p className="text-base md:text-lg lg:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Explore our portfolio of successful MEP engineering projects
              across diverse industries, showcasing innovation, technical
              excellence, and sustainable solutions.
            </p>

            {/* Project Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 pt-6 md:pt-8 max-w-4xl mx-auto"
            >
              <div className="text-center">
                <div className="text-xl md:text-2xl lg:text-3xl font-bold text-white">500+</div>
                <div className="text-xs md:text-sm text-gray-300">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-xl md:text-2xl lg:text-3xl font-bold text-white">$50M+</div>
                <div className="text-xs md:text-sm text-gray-300">Project Value</div>
              </div>
              <div className="text-center">
                <div className="text-xl md:text-2xl lg:text-3xl font-bold text-white">98%</div>
                <div className="text-xs md:text-sm text-gray-300">Client Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-xl md:text-2xl lg:text-3xl font-bold text-white">15+</div>
                <div className="text-xs md:text-sm text-gray-300">Years Experience</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-6 md:py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap items-center justify-center gap-2 md:gap-4">
            <div className="flex items-center space-x-2 text-gray-600 mb-2 md:mb-0">
              <Filter className="w-4 h-4 md:w-5 md:h-5" />
              <span className="font-medium text-sm md:text-base">Project Type:</span>
            </div>
            {projectFilter.map((filter) => (
              <motion.button
                key={filter.label}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleFilterChange(filter.filter)}
                className={`px-3 md:px-4 py-1.5 md:py-2 rounded-full font-medium transition-colors text-sm md:text-base ${
                  selectedProjectFilter === filter.filter
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {filter.label}
              </motion.button>
            ))}
          </div>
          
          {/* Results count */}
          {/* <div className="text-center mt-4 text-gray-600">
            {selectedProjectFilter === "all" ? (
              <span>Showing all {filteredProjects.length} projects</span>
            ) : (
              <span>
                Showing {filteredProjects.length} projects in{" "}
                {projectFilter.find(f => f.filter === selectedProjectFilter)?.label}
              </span>
            )}
          </div> */}
        </div>
      </section>

      {/* Projects Grid */}
      <section ref={projectsRef} className="py-12 md:py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredProjects.length === 0 ? (
            <div className="text-center py-8 md:py-12">
              <p className="text-gray-500 text-base md:text-lg">No projects found for the selected filter.</p>
            </div>
          ) : (
            <>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-12">
                {currentProjects.map((project, index) => (
                  <motion.div
                    key={project.slug}
                    initial={{ opacity: 0, y: 20 }}
                    animate={projectsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer hover:shadow-xl transition-shadow duration-300"
                    onClick={() => handleProjectClick(project)}
                  >
                    {/* Project Image */}
                    <div className="h-40 md:h-48 bg-gray-200 relative">
                      <img
                        src={project.images?.[0] || "/api/placeholder/400/300"}
                        alt={project.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 md:top-4 right-2 md:right-4">
                        <span className={`px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium ${
                          project.status === "Completed"
                            ? "bg-green-100 text-green-800"
                            : "bg-blue-100 text-blue-800"
                        }`}>
                          {project.status}
                        </span>
                      </div>
                      {/* <div className="absolute top-4 right-4">
                        <span className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-sm font-medium">
                          {project.projecttype.toUpperCase()}
                        </span>
                      </div> */}
                    </div>

                    {/* Project Details */}
                    <div className="p-4 md:p-6">
                      <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-3 md:mb-4">{project.title}</h3>
                      <div className="space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-600">
                        {project.location && (
                          <p className="flex items-center">
                            <span className="font-medium">Location:</span>
                            <span className="ml-2">{project.location}</span>
                          </p>
                        )}
                        {project.client && (
                          <p className="flex items-center">
                            <span className="font-medium">Client:</span>
                            <span className="ml-2">{project.client}</span>
                          </p>
                        )}

                      </div>
                      <div className="mt-3 md:mt-4">
                        <button className="text-blue-600 hover:text-blue-700 font-medium text-xs md:text-sm">
                          View Details →
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center mt-8 md:mt-12 space-x-1 md:space-x-2">
                  {/* Previous Button */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`flex items-center px-2 md:px-3 py-1.5 md:py-2 rounded-lg font-medium transition-colors text-sm md:text-base ${
                      currentPage === 1
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <ChevronLeft className="w-3 h-3 md:w-4 md:h-4 mr-1" />
                    <span className="hidden sm:inline">Previous</span>
                    <span className="sm:hidden">Prev</span>
                  </button>

                  {/* Page Numbers */}
                  {getPaginationButtons().map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-2.5 md:px-4 py-1.5 md:py-2 rounded-lg font-medium transition-colors text-sm md:text-base ${
                        currentPage === page
                          ? "bg-blue-600 text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      {page}
                    </button>
                  ))}

                  {/* Next Button */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`flex items-center px-2 md:px-3 py-1.5 md:py-2 rounded-lg font-medium transition-colors text-sm md:text-base ${
                      currentPage === totalPages
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <span className="hidden sm:inline">Next</span>
                    <span className="sm:hidden">Next</span>
                    <ChevronRight className="w-3 h-3 md:w-4 md:h-4 ml-1" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-4 sm:px-6 md:px-8 lg:px-10 py-4 md:py-5">
        <div className="max-w-7xl bg-slate-800 mx-auto px-4 py-8 md:py-10 sm:py-5 lg:py-8 rounded-xl md:rounded-2xl sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={projectsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="space-y-4 md:space-y-6"
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
              Have a Project in Mind?
            </h2>
            <p className="text-base md:text-lg lg:text-xl text-blue-100 max-w-2xl mx-auto">
              Let's discuss how we can bring your vision to life with our
              expertise and proven track record.
            </p>
            <Link href="/enquire#enquiry-form">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 text-white bg-blue-600 font-semibold rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base"
              >
                Enquire Now
                <ExternalLink className="ml-2 w-4 h-4 md:w-5 md:h-5" />
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Project Dialog */}
      <ProjectDialog
        project={selectedProject}
        isOpen={isDialogOpen}
        onClose={closeDialog}
      />
    </div>
  );
};

export default ProjectsPage;


const ProjectDialog = ({ project, isOpen, onClose }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  if (!project) return null;

  const nextImage = () => {
    if (project.images.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % project.images.length);
    }
  };

  const prevImage = () => {
    if (project.images.length > 1) {
      setCurrentImageIndex(
        (prev) => (prev - 1 + project.images.length) % project.images.length
      );
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            onClick={onClose}
          >
            {/* Dialog Content */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-lg max-w-5xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header with Close Button */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">
                  {project.title}
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <div className="flex flex-col lg:flex-row">
                {/* Left Side - Images */}
                <div className="lg:w-1/2 p-6">
                  <div className="h-80 lg:h-96 relative">
                    <div className="w-full h-full bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={
                          project.images[currentImageIndex] ||
                          "/api/placeholder/600/400"
                        }
                        alt={`${project.title} - Image ${
                          currentImageIndex + 1
                        }`}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Navigation Arrows - Only show if more than 1 image */}
                    {project.images.length > 1 && (
                      <>
                        <button
                          onClick={prevImage}
                          className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors"
                        >
                          <ChevronLeft className="w-5 h-5" />
                        </button>
                        <button
                          onClick={nextImage}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors"
                        >
                          <ChevronRight className="w-5 h-5" />
                        </button>
                      </>
                    )}

                    {/* Dots Indicator - Only show if more than 1 image */}
                    {project.images.length > 1 && (
                      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        {project.images.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            className={`w-2 h-2 rounded-full transition-colors ${
                              index === currentImageIndex
                                ? "bg-white"
                                : "bg-white/50"
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Side - Project Details */}
                <div className="lg:w-1/2 p-6">
                  <div className="space-y-6">
                    {/* Project Details Table */}
                    <div className="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden">
                      {/* CLIENT */}
                      <div className="border-b border-gray-200 p-4">
                        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                          CLIENT
                        </div>
                        <div className="text-gray-900 font-medium">
                          {project.client}
                        </div>
                      </div>

                      {/* LOCATION */}
                      <div className="border-b border-gray-200 p-4">
                        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                          LOCATION
                        </div>
                        <div className="text-gray-900 font-medium">
                          {project.location}
                        </div>
                      </div>

                      {/* STATUS */}
                      <div className="border-b border-gray-200 p-4">
                        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                          STATUS
                        </div>
                        <div className="text-gray-900 font-medium">
                          {project.status}
                        </div>
                      </div>

                      {/* PROJECT TYPE */}
                      <div className="border-b border-gray-200 p-4">
                        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                          PROJECT TYPE
                        </div>
                        <div className="text-gray-900 font-medium">
                          {project.projecttype}
                        </div>
                      </div>

                      {/* AREA */}
                      <div
                        className={`p-4 ${
                          project.Floors ? "border-b border-gray-200" : ""
                        }`}
                      >
                        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                          AREA
                        </div>
                        <div className="text-gray-900 font-medium">
                          {project.Area}
                        </div>
                      </div>

                      {/* FLOORS - Only show if exists */}
                      {project.Floors && (
                        <div className="p-4">
                          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                            FLOORS
                          </div>
                          <div className="text-gray-900 font-medium">
                            {project.Floors}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Action Button */}
                    <div className="pt-4">
                      <button className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors">
                        Contact Us for Similar Project
                        <ExternalLink className="ml-2 w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
