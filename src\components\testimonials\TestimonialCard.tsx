'use client'

import { motion } from 'motion/react'
import { Quote, Building2, MapPin, Clock } from 'lucide-react'

interface Testimonial {
  id: number
  company: string
  location: string
  testimonial: string
  duration: string
  category: string
  logo?: string
}

interface TestimonialCardProps {
  testimonial: Testimonial
}

export function TestimonialCard({ testimonial }: TestimonialCardProps) {
  return (
    <motion.div
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-slate-200"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-xl font-bold text-slate-800 mb-1">
            {testimonial.company}
          </h3>
          <div className="flex items-center text-slate-600 text-sm mb-2">
            <MapPin className="w-4 h-4 mr-1" />
            {testimonial.location}
          </div>
          <div className="flex items-center text-slate-600 text-sm">
            <Building2 className="w-4 h-4 mr-1" />
            {testimonial.category}
          </div>
        </div>
        <div className="text-blue-600">
          <Quote className="w-8 h-8" />
        </div>
      </div>

      {/* Testimonial Text */}
      <div className="mb-4">
        <p className="text-slate-700 leading-relaxed italic">
          "{testimonial.testimonial}"
        </p>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-slate-100">
        <div className="flex items-center text-slate-600 text-sm">
          <Clock className="w-4 h-4 mr-1" />
          {testimonial.duration}
        </div>
        <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
          Verified Client
        </div>
      </div>
    </motion.div>
  )
}
