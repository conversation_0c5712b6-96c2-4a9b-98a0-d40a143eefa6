// Animation variants and utilities for Framer Motion

export const fadeInUp = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -30 }
}

export const fadeInDown = {
  initial: { opacity: 0, y: -30 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 30 }
}

export const fadeInLeft = {
  initial: { opacity: 0, x: -30 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 30 }
}

export const fadeInRight = {
  initial: { opacity: 0, x: 30 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -30 }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 }
}

export const slideInUp = {
  initial: { y: '100%' },
  animate: { y: 0 },
  exit: { y: '100%' }
}

export const slideInDown = {
  initial: { y: '-100%' },
  animate: { y: 0 },
  exit: { y: '-100%' }
}

// Stagger container variants
export const staggerContainer = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Page transition variants
export const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  },
  exit: { 
    opacity: 0, 
    y: -20,
    transition: {
      duration: 0.3
    }
  }
}

// Hover animations
export const hoverScale = {
  scale: 1.05,
  transition: { duration: 0.2 }
}

export const hoverLift = {
  y: -5,
  scale: 1.02,
  transition: { duration: 0.2 }
}

export const hoverGlow = {
  scale: 1.02,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
  transition: { duration: 0.3 }
}

// Button animations
export const buttonTap = {
  scale: 0.95
}

export const buttonHover = {
  scale: 1.05,
  transition: { duration: 0.2 }
}

// Card animations
export const cardHover = {
  y: -10,
  scale: 1.02,
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  transition: { duration: 0.3 }
}

// Text animations
export const textReveal = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut'
    }
  }
}

// Loading animations
export const spinAnimation = {
  animate: { rotate: 360 },
  transition: { duration: 1, repeat: Infinity, ease: 'linear' }
}

export const pulseAnimation = {
  animate: {
    scale: [1, 1.1, 1],
    opacity: [0.7, 1, 0.7]
  },
  transition: {
    duration: 1.5,
    repeat: Infinity,
    ease: 'easeInOut'
  }
}

// Carousel animations
export const carouselSlide = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0
  }),
  center: {
    zIndex: 1,
    x: 0,
    opacity: 1
  },
  exit: (direction: number) => ({
    zIndex: 0,
    x: direction < 0 ? 1000 : -1000,
    opacity: 0
  })
}

// Easing functions
export const easing = {
  easeInOut: [0.25, 0.46, 0.45, 0.94],
  easeOut: [0.25, 0.46, 0.45, 0.94],
  easeIn: [0.42, 0, 1, 1],
  bounce: [0.68, -0.55, 0.265, 1.55]
}

// Duration constants
export const duration = {
  fast: 0.2,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8
}

// Utility function to create custom animations
export const createAnimation = (
  initial: any,
  animate: any,
  exit?: any,
  transition?: any
) => ({
  initial,
  animate,
  exit: exit || initial,
  transition: {
    duration: duration.normal,
    ease: easing.easeInOut,
    ...transition
  }
})

// Scroll-triggered animation utility
export const createScrollAnimation = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  distance: number = 50,
  delay: number = 0
) => {
  const getInitial = () => {
    switch (direction) {
      case 'up': return { opacity: 0, y: distance }
      case 'down': return { opacity: 0, y: -distance }
      case 'left': return { opacity: 0, x: distance }
      case 'right': return { opacity: 0, x: -distance }
    }
  }

  const getAnimate = () => {
    switch (direction) {
      case 'up':
      case 'down': return { opacity: 1, y: 0 }
      case 'left':
      case 'right': return { opacity: 1, x: 0 }
    }
  }

  return {
    initial: getInitial(),
    animate: getAnimate(),
    transition: {
      duration: duration.slow,
      delay,
      ease: easing.easeOut
    }
  }
}
